import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 查询指定ID的足迹记录详情
export async function POST(req: NextRequest) {
  try {
    const { footprintId } = await req.json();

    if (!footprintId) {
      return NextResponse.json({ error: "缺少足迹记录ID" }, { status: 400 });
    }

    // 查询足迹记录
    const footprint = await prisma.userFootprints.findUnique({
      where: { id: footprintId },
      include: {
        user: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
      },
    });

    if (!footprint) {
      return NextResponse.json({ error: "足迹记录不存在" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: footprint,
    });
  } catch (error) {
    console.error("查询足迹记录失败:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}
