import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 获取用户获得的所有卡片（包括创建的和接收的）
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 查询用户获得的所有卡片
    const userCards = await prisma.cardAcquisitionRecord.findMany({
      where: { userId },
      include: {
        card: {
          include: {
            author: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
        },
      },
      orderBy: {
        acquiredAt: "desc", // 按获取时间倒序排列
      },
    });

    // 格式化返回数据，合并卡片信息和获得记录
    const formattedCards = userCards.map((userCard) => ({
      // 卡片基本信息
      id: userCard.card.id,
      title: userCard.card.title,
      description: userCard.card.description,
      cardType: userCard.card.cardType,
      themeColor: userCard.card.themeColor,
      coinReward: userCard.card.coinReward,
      experienceReward: userCard.card.experienceReward,
      imageFileName: userCard.card.imageFileName,
      imageURL: userCard.card.imageURL,
      location: userCard.card.location,
      latitude: userCard.card.latitude,
      longitude: userCard.card.longitude,
      createdAt: userCard.card.createdAt,
      // 作者信息
      author: userCard.card.author,
      // 获得记录信息
      acquiredAt: userCard.acquiredAt,
      isAuthor: userCard.isAuthor,
      acquisitionRecordId: userCard.id, // 获得记录ID
    }));

    return NextResponse.json({
      success: true,
      data: {
        cards: formattedCards,
        total: formattedCards.length,
      },
    });
  } catch (error) {
    console.error("查询用户卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 删除用户的卡片获得记录（只删除CardAcquisitionRecord记录，不删除原始卡片）
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const cardId = searchParams.get("cardId");

    if (!userId || !cardId) {
      return NextResponse.json(
        { error: "缺少userId或cardId参数" },
        { status: 400 }
      );
    }

    // 验证用户获得了该卡片
    const acquisitionRecord = await prisma.cardAcquisitionRecord.findUnique({
      where: {
        userId_cardId: {
          userId: userId,
          cardId: cardId,
        },
      },
    });

    if (!acquisitionRecord) {
      return NextResponse.json({ error: "用户未获得该卡片" }, { status: 404 });
    }

    // 使用事务删除用户获得记录和相关日志记录（不删除原始卡片）
    await prisma.$transaction(async (tx) => {
      // 删除相关的日志记录（通过itemCardId关联）
      await tx.userLogs.deleteMany({
        where: {
          userId: userId,
          itemCardId: cardId,
        },
      });

      // 删除用户获得记录
      await tx.cardAcquisitionRecord.delete({
        where: {
          userId_cardId: {
            userId: userId,
            cardId: cardId,
          },
        },
      });
    });

    return NextResponse.json({
      success: true,
      message: "卡片已从个人收藏中移除，相关日志已删除",
    });
  } catch (error) {
    console.error("删除用户卡片获得记录失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}
