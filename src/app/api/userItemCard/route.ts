import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 获取用户持有的所有卡片（包括创建的和接收的）
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 查询用户持有的所有卡片
    const userCards = await prisma.userItemCard.findMany({
      where: { userId },
      include: {
        card: {
          include: {
            author: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
        },
      },
      orderBy: {
        acquiredAt: "desc", // 按获取时间倒序排列
      },
    });

    // 格式化返回数据，合并卡片信息和用户备注
    const formattedCards = userCards.map((userCard) => ({
      // 卡片基本信息
      id: userCard.card.id,
      title: userCard.card.title,
      description: userCard.card.description,
      tags: userCard.card.tags,
      imageFileName: userCard.card.imageFileName,
      imageURL: userCard.card.imageURL,
      location: userCard.card.location,
      latitude: userCard.card.latitude,
      longitude: userCard.card.longitude,
      createdAt: userCard.card.createdAt,
      // 作者信息
      author: userCard.card.author,
      // 用户特定信息
      remark: userCard.remark,
      acquiredAt: userCard.acquiredAt,
      isOwner: userCard.isOwner,
      userItemCardId: userCard.id, // 用于后续修改备注
    }));

    return NextResponse.json({
      success: true,
      data: {
        cards: formattedCards,
        total: formattedCards.length,
      },
    });
  } catch (error) {
    console.error("查询用户卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}

// 修改用户卡片的备注
export async function PATCH(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const cardId = searchParams.get("cardId");

    if (!userId || !cardId) {
      return NextResponse.json(
        { error: "缺少userId或cardId参数" },
        { status: 400 },
      );
    }

    const { remark } = await req.json();

    // 验证用户持有该卡片
    const userCard = await prisma.userItemCard.findUnique({
      where: {
        userId_cardId: {
          userId: userId,
          cardId: cardId,
        },
      },
    });

    if (!userCard) {
      return NextResponse.json({ error: "用户未持有该卡片" }, { status: 404 });
    }

    // 更新备注
    const updatedUserCard = await prisma.userItemCard.update({
      where: {
        userId_cardId: {
          userId: userId,
          cardId: cardId,
        },
      },
      data: {
        remark: remark || "",
      },
      include: {
        card: {
          include: {
            author: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
        },
      },
    });

    // 格式化返回数据
    const formattedCard = {
      id: updatedUserCard.card.id,
      title: updatedUserCard.card.title,
      description: updatedUserCard.card.description,
      tags: updatedUserCard.card.tags,
      imageFileName: updatedUserCard.card.imageFileName,
      imageURL: updatedUserCard.card.imageURL,
      location: updatedUserCard.card.location,
      latitude: updatedUserCard.card.latitude,
      longitude: updatedUserCard.card.longitude,
      createdAt: updatedUserCard.card.createdAt,
      author: updatedUserCard.card.author,
      remark: updatedUserCard.remark,
      acquiredAt: updatedUserCard.acquiredAt,
      isOwner: updatedUserCard.isOwner,
      userItemCardId: updatedUserCard.id,
    };

    return NextResponse.json({
      success: true,
      message: "备注更新成功",
      data: formattedCard,
    });
  } catch (error) {
    console.error("更新备注失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}

// 删除用户持有的卡片（只删除UserItemCard记录，不删除原始卡片）
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");
    const cardId = searchParams.get("cardId");

    if (!userId || !cardId) {
      return NextResponse.json(
        { error: "缺少userId或cardId参数" },
        { status: 400 },
      );
    }

    // 验证用户持有该卡片
    const userCard = await prisma.userItemCard.findUnique({
      where: {
        userId_cardId: {
          userId: userId,
          cardId: cardId,
        },
      },
    });

    if (!userCard) {
      return NextResponse.json({ error: "用户未持有该卡片" }, { status: 404 });
    }

    // 删除用户持有记录（不删除原始卡片）
    await prisma.userItemCard.delete({
      where: {
        userId_cardId: {
          userId: userId,
          cardId: cardId,
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "卡片已从个人收藏中移除",
    });
  } catch (error) {
    console.error("删除用户卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 },
    );
  }
}
