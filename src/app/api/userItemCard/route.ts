import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 获取用户获得的所有卡片（包括创建的和接收的）
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return NextResponse.json({ error: "缺少userId参数" }, { status: 400 });
    }

    // 验证用户存在
    const user = await prisma.user.findUnique({
      where: { userId },
    });

    if (!user) {
      return NextResponse.json({ error: "用户不存在" }, { status: 404 });
    }

    // 查询用户获得的所有卡片
    const userCards = await prisma.cardAcquisitionRecord.findMany({
      where: { userId },
      include: {
        card: {
          include: {
            author: {
              select: {
                userId: true,
                nickname: true,
                avatarURL: true,
              },
            },
          },
        },
      },
      orderBy: {
        acquiredAt: "desc", // 按获取时间倒序排列
      },
    });

    // 格式化返回数据，合并卡片信息和获得记录
    const formattedCards = userCards.map((userCard) => ({
      // 卡片基本信息
      id: userCard.card.id,
      title: userCard.card.title,
      description: userCard.card.description,
      cardType: userCard.card.cardType,
      themeColor: userCard.card.themeColor,
      coinReward: userCard.card.coinReward,
      experienceReward: userCard.card.experienceReward,
      imageFileName: userCard.card.imageFileName,
      imageURL: userCard.card.imageURL,
      location: userCard.card.location,
      latitude: userCard.card.latitude,
      longitude: userCard.card.longitude,
      createdAt: userCard.card.createdAt,
      // 作者信息
      author: userCard.card.author,
      // 获得记录信息
      acquiredAt: userCard.acquiredAt,
      isAuthor: userCard.isAuthor,
      acquisitionRecordId: userCard.id, // 获得记录ID
    }));

    return NextResponse.json({
      success: true,
      data: {
        cards: formattedCards,
        total: formattedCards.length,
      },
    });
  } catch (error) {
    console.error("查询用户卡片失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 注意：由于新的卡片逻辑不再支持个人备注和删除卡片功能，PATCH和DELETE方法已被移除
// 卡片现在是统一的，不再有个性化内容，用户只能查看获得的卡片
