# API 接口文档

## 好友系统相关 API

### 1. 获取用户基本信息

- **接口路径**: `GET /api/users`
- **功能**: 根据 userId 获取用户基本信息，用于添加好友时的用户搜索
- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 要查询的用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      userId: string;
      nickname: string;
      avatar: string | null;
    }
  }

  // 错误响应
  {
    error: string; // 错误信息
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 缺少参数
  - `404`: 用户不存在
  - `500`: 服务器错误

### 2. 获取好友列表

- **接口路径**: `GET /api/friends`
- **功能**: 获取用户的所有好友关系，包括已接受、待处理和已拒绝的请求
- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 当前用户ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      accepted: Array<{
        friendshipId: string;
        friend: {
          userId: string;
          nickname: string;
          avatar: string | null;
        };
        status: "accepted";
        createdAt: Date;
        updatedAt: Date;
        type: "sent" | "received"; // 请求方向
      }>;
      pending: Array<{
        friendshipId: string;
        friend: {
          userId: string;
          nickname: string;
          avatar: string | null;
        };
        status: "pending";
        createdAt: Date;
        updatedAt: Date;
        type: "sent" | "received";
      }>;
      rejected: Array<{
        friendshipId: string;
        friend: {
          userId: string;
          nickname: string;
          avatar: string | null;
        };
        status: "rejected";
        createdAt: Date;
        updatedAt: Date;
        type: "sent" | "received";
      }>;
    }
  }
  ```

### 3. 发送好友请求

- **接口路径**: `POST /api/friends`
- **功能**: 向指定用户发送好友请求
- **测试性 Curl：**

```bash
curl -i -X post "http://*************:3000/api/friends" \
  -H "content-type: application/json" \
  -d '{"userId":"alice","friendId":"bob"}'

```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 发起请求的用户ID
    friendId: string; // 目标用户ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "好友请求发送成功";
    data: {
      id: string;
      userId: string;
      friendId: string;
      status: "pending";
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误或关系已存在
  - `404`: 目标用户不存在
  - `500`: 服务器错误

### 4. 处理好友请求

- **接口路径**: `PUT /api/friends`
- **功能**: 同意或拒绝收到的好友请求
- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 当前用户ID（处理请求的用户id）
    friendId: string; // 发起请求的用户ID
    action: "accept" | "reject"; // 操作类型
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "已同意好友请求" | "已拒绝好友请求";
    data: {
      id: string;
      userId: string;
      friendId: string;
      status: "accepted" | "rejected";
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```

### 5. 删除好友关系

- **接口路径**: `DELETE /api/friends`
- **功能**: 删除已建立的好友关系
- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 当前用户ID
    friendId: string; // 要删除的好友ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "好友关系已删除";
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误
  - `404`: 好友关系不存在
  - `500`: 服务器错误

### 6. 批量获取好友位置信息

- **接口路径**: `POST /api/friendsLocation`
- **功能**: 批量获取开启位置共享的好友的位置和基本信息
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/friendsLocation" \
  -H "content-type: application/json" \
  -d '{"friendIds":["alice","bob","charlie"]}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    friendIds: string[]; // 好友用户ID数组
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      friends: Array<{
        userId: string; // 用户ID
        nickname: string; // 昵称
        avatarURL: string | null; // 头像URL
        lastActiveTime: Date | null; // 最后活跃时间
        latitude: number; // 纬度
        longitude: number; // 经度
        lastUpdate: Date; // 位置最后更新时间
        lastOnlineTime: Date; // 最后在线时间
      }>;
      total: number; // 返回的好友数量
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误（缺少 friendIds 或格式错误）
  - `500`: 服务器错误

**注意事项**:

- 只返回开启位置共享（`sharingLocation: true`）的好友信息
- 只返回有位置记录的好友
- 如果好友关闭了位置共享或没有位置记录，不会出现在返回结果中

## 用户卡片持有系统相关 API

### 12. 获取用户持有的所有卡片

- **接口路径**: `GET /api/userItemCard`
- **功能**: 获取用户持有的所有卡片（包括自己创建的和接收到的），返回完整的卡片信息和用户个人备注
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/userItemCard?userId=alice"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      cards: Array<{
        // 卡片基本信息
        id: string;
        title: string;
        description: string;
        tags: any;
        imageFileName: string;
        imageURL: string;
        location: string;
        latitude: number | null;
        longitude: number | null;
        createdAt: Date;
        // 作者信息
        author: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
        // 用户特定信息
        remark: string | null; // 用户个人备注
        acquiredAt: Date; // 获取时间
        isOwner: boolean; // 是否是作者
        userItemCardId: string; // 用于后续修改备注
      }>;
      total: number;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少参数
  - `404`: 用户不存在
  - `500`: 服务器错误

### 13. 修改用户卡片备注

- **接口路径**: `PATCH /api/userItemCard`
- **功能**: 修改用户对特定卡片的个人备注
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/userItemCard?userId=alice&cardId=card123" \
  -H "content-type: application/json" \
  -d '{"remark":"这是我的个人备注"}'
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID
    cardId: string; // 卡片ID
  }
  // Body 参数
  {
    remark: string; // 新的备注内容
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "备注更新成功";
    data: {
      // 完整的卡片信息（同GET接口返回格式）
      id: string;
      title: string;
      description: string;
      // ... 其他字段
      remark: string; // 更新后的备注
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少参数
  - `404`: 用户未持有该卡片
  - `500`: 服务器错误

### 14. 删除用户持有的卡片

- **接口路径**: `DELETE /api/userItemCard`
- **功能**: 从用户的持有列表中移除卡片（只删除持有关系，不删除原始卡片）
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/userItemCard?userId=alice&cardId=card123"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID
    cardId: string; // 卡片ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "卡片已从个人收藏中移除";
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少参数
  - `404`: 用户未持有该卡片
  - `500`: 服务器错误

## 卡片管理系统相关 API

### 15. 修改卡片信息

- **接口路径**: `PATCH /api/itemCard`
- **功能**: 修改卡片的基本信息（只有作者可以修改）
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/itemCard?userId=alice&cardId=card123" \
  -H "content-type: application/json" \
  -d '{"title":"新标题","description":"新描述"}'
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必须是作者）
    cardId: string; // 卡片ID
  }
  // Body 参数（所有字段都是可选的）
  {
    tags?: any;
    description?: string;
    title?: string;
    imageFileName?: string;
    imageURL?: string;
    location?: string;
    latitude?: number;
    longitude?: number;
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "卡片信息更新成功";
    data: {
      id: string;
      tags: any;
      description: string;
      title: string;
      imageFileName: string;
      imageURL: string;
      location: string;
      latitude: number | null;
      longitude: number | null;
      createdAt: Date;
      authorId: string;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少参数
  - `403`: 只有作者可以修改卡片信息
  - `404`: 卡片不存在
  - `500`: 服务器错误

**重要变更说明**:

1. **卡片创建流程变更**: 创建卡片时会自动在 `UserItemCard` 表中创建作者的持有记录
2. **传输接受流程变更**: 接受卡片传输时会在 `UserItemCard` 表中创建接收者的持有记录
3. **备注系统**: 每个用户对同一张卡片可以有独立的备注，不会相互影响
4. **引用机制**: 卡片数据保持唯一，用户通过 `UserItemCard` 表引用，避免数据冗余

## 卡片传输系统相关 API

### 7. 创建卡片传输请求

- **接口路径**: `POST /api/itemcard-transfers`
- **功能**: 创建卡片传输请求，向指定用户发送卡片
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/itemcard-transfers" \
  -H "content-type: application/json" \
  -d '{"senderId":"alice","receiverId":"bob","cardId":"card123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    senderId: string; // 发送者用户ID
    receiverId: string; // 接收者用户ID
    cardId: string; // 要传输的卡片ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "卡片传输请求创建成功";
    data: {
      id: string; // 传输记录ID
      senderId: string; // 发送者ID
      receiverId: string; // 接收者ID
      cardId: string; // 卡片ID
      transferTime: Date; // 传输创建时间
      status: "pending"; // 传输状态
      sender: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      receiver: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      card: {
        id: string;
        title: string;
        description: string;
        imageURL: string;
        tags: any;
      }
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误或重复传输
  - `404`: 用户或卡片不存在
  - `500`: 服务器错误

### 8. 处理传输请求

- **接口路径**: `PATCH /api/itemcard-transfers`
- **功能**: 接受或拒绝收到的卡片传输请求（接受时卡片直接转移，保持原始作者不变）
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/itemcard-transfers" \
  -H "content-type: application/json" \
  -d '{"transferId":"transfer123","action":"accept","userId":"bob"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    transferId: string; // 传输记录ID
    action: "accept" | "reject"; // 操作类型
    userId: string; // 当前用户ID（必须是接收者）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "卡片传输已接受，卡片已转移" | "卡片传输已拒绝";
    data: {
      id: string;
      senderId: string;
      receiverId: string;
      cardId: string;
      transferTime: Date;
      status: "accepted" | "rejected";
      sender: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      receiver: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      card: {
        id: string;
        title: string;
        description: string;
        imageURL: string;
        tags: any;
      }
      // 注意：接受传输时不创建新卡片，卡片直接转移
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误或请求已处理
  - `403`: 只有接收者可以处理请求
  - `404`: 传输请求不存在
  - `500`: 服务器错误

### 9. 查询接收到的传输记录

- **接口路径**: `GET /api/itemcard-transfers/received`
- **功能**: 查询用户接收到的卡片传输记录
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/itemcard-transfers/received?userId=bob&status=pending"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string;                              // 用户ID
    status?: "pending" | "accepted" | "rejected"; // 可选：过滤特定状态
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      transfers: {
        pending: Array<TransferRecord>;    // 待处理的传输
        accepted: Array<TransferRecord>;   // 已接受的传输
        rejected: Array<TransferRecord>;   // 已拒绝的传输
      } | Array<TransferRecord>;           // 如果指定了status，直接返回数组
      total: number;                       // 总记录数
    }
  }

  // TransferRecord 类型
  interface TransferRecord {
    id: string;
    senderId: string;
    receiverId: string;
    cardId: string;
    transferTime: Date;
    status: "pending" | "accepted" | "rejected";
    sender: {
      userId: string;
      nickname: string;
      avatarURL: string | null;
    };
    card: {
      id: string;
      title: string;
      description: string;
      imageURL: string;
      tags: any;
      location: string;
      createdAt: Date;
    };
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 缺少 userId 参数
  - `404`: 用户不存在
  - `500`: 服务器错误

### 10. 查询发送的传输记录

- **接口路径**: `GET /api/itemcard-transfers/sent`
- **功能**: 查询用户发送的卡片传输记录
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/itemcard-transfers/sent?userId=alice&status=accepted"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string;                              // 用户ID
    status?: "pending" | "accepted" | "rejected"; // 可选：过滤特定状态
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      transfers: {
        pending: Array<SentTransferRecord>;    // 待处理的传输
        accepted: Array<SentTransferRecord>;   // 已接受的传输
        rejected: Array<SentTransferRecord>;   // 已拒绝的传输
      } | Array<SentTransferRecord>;           // 如果指定了status，直接返回数组
      total: number;                           // 总记录数
    }
  }

  // SentTransferRecord 类型
  interface SentTransferRecord {
    id: string;
    senderId: string;
    receiverId: string;
    cardId: string;
    transferTime: Date;
    status: "pending" | "accepted" | "rejected";
    receiver: {
      userId: string;
      nickname: string;
      avatarURL: string | null;
    };
    card: {
      id: string;
      title: string;
      description: string;
      imageURL: string;
      tags: any;
      location: string;
      createdAt: Date;
    };
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 缺少 userId 参数
  - `404`: 用户不存在
  - `500`: 服务器错误

## 图片上传相关 API

### 上传图片到腾讯云 COS

- **接口路径**: `POST /api/upload/image`
- **功能**: 上传图片到腾讯云 COS 存储，返回可访问的 URL
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/upload/image" \
  -H "content-type: multipart/form-data" \
  -F "image=@/path/to/your/image.jpg"

```

- **请求参数**:

  ```typescript
  // Form Data 参数
  {
    image: File; // 图片文件，支持 JPEG、PNG、WebP 格式，最大 10MB
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "图片上传成功";
    data: {
      url: string;          // COS 返回的完整访问 URL
      filename: string;     // 在 COS 中的文件名
      originalName: string; // 原始文件名
      size: number;         // 文件大小（字节）
      type: string;         // 文件 MIME 类型
    };
  }

  // 错误响应
  {
    success: false;
    message: string; // 错误信息
    error?: string;  // 详细错误信息（仅在服务器错误时返回）
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误（文件格式不支持、文件过大等）
  - `500`: 服务器错误

### 在腾讯云中删除图片

- 接口路径：`DELETE /api/upload/image`

请求参数：

```
// Body参数
{
  filename: String
}
```

注意，同时支持两种格式：

- `https://carboncoin-cos-1358266118.cos.ap-shanghai.myqcloud.com/images/1757146615104_z7ew0v0q71k.jpg`
- `images/1757146470194_z3pywm1pv9.jpg`

因此可以直接使用存储的 avatarURL 等 URL 数据来传递

### 环境变量配置

使用图片上传功能需要在 `.env` 文件中配置以下腾讯云 COS 相关环境变量：

```env
# 腾讯云 API 密钥
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key

# COS 存储桶配置
COS_BUCKET=your-bucket-name-1250000000
COS_REGION=ap-beijing
```

## 使用说明

### 好友系统工作流程

1. **搜索用户**: 使用 `GET /api/users?userId=xxx` 获取用户信息
2. **发送好友请求**: 使用 `POST /api/friends` 发送请求
3. **查看好友列表**: 使用 `GET /api/friends?userId=xxx` 查看所有关系
4. **处理请求**: 使用 `PUT /api/friends` 同意或拒绝请求
5. **删除好友**: 使用 `DELETE /api/friends` 删除关系
6. **获取好友位置**: 使用 `POST /api/friendsLocation` 批量获取好友位置信息

### 好友位置查询工作流程

1. **获取好友列表**: 先调用 `GET /api/friends` 获取已接受的好友关系
2. **提取好友 ID**: 从好友列表中提取所有好友的 `userId`，组成数组
3. **批量查询位置**: 调用 `POST /api/friendsLocation` 传入好友 ID 数组
4. **处理返回结果**: 只会返回开启位置共享且有位置记录的好友信息

### 卡片传输工作流程

1. **创建传输请求**: 使用 `POST /api/itemcard-transfers` 发送卡片给好友
2. **查看传输状态**: 发送者使用 `GET /api/itemcard-transfers/sent` 查看发送记录
3. **处理传输请求**: 接收者使用 `GET /api/itemcard-transfers/received` 查看收到的请求
4. **接受或拒绝**: 接收者使用 `PATCH /api/itemcard-transfers` 处理请求
5. **获得卡片副本**: 接受后系统会为接收者创建卡片副本

### 注意事项

- 所有接口都需要提供有效的 userId
- 好友关系是单向存储的，但查询时会双向检查
- 删除好友关系会完全移除数据库记录
- 拒绝的好友请求会保留在数据库中，状态为 "rejected"
- 位置共享功能受用户的 `sharingLocation` 字段控制，默认开启
- 批量位置查询只返回同时满足以下条件的好友：
  - 开启了位置共享（`sharingLocation: true`）
  - 在 UserLocation 表中有位置记录
- 前端应该优雅处理部分好友不返回位置信息的情况
- 卡片传输系统的注意事项：
  - 卡片可以自由传输，不限制只能传输自己创建的卡片
  - 接受传输时卡片直接转移，不创建副本
  - 卡片的原始作者（authorId）永远不变，保持创建者信息
  - 传输请求有三种状态：pending（待处理）、accepted（已接受）、rejected（已拒绝）
  - 只有接收者可以处理传输请求
  - 重复的传输请求会被拒绝

## 足迹追踪系统相关 API

### 16. 创建足迹记录

- **接口路径**: `POST /api/footprints`
- **功能**: 创建新的足迹记录，开始一次出行追踪
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/footprints" \
  -H "content-type: application/json" \
  -d '{"userId":"alice","activityType":"walking","isFinished":false,"footPrints":[{"latitude":39.9042,"longitude":116.4074}]}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 用户ID
    activityType: "walking" | "cycling" | "bus" | "subway"; // 出行活动类型（必需）
    isFinished?: boolean; // 是否已完成此次出行，默认false
    footPrints?: Array<{
      latitude: number; // 纬度（必需）
      longitude: number; // 经度（必需）
      // timestamp 由后端自动生成
    }>; // 足迹点数组，可选，默认为空数组
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "足迹记录创建成功";
    data: {
      id: string; // 足迹记录ID
      userId: string;
      footPrints: Array<{
        latitude: number;
        longitude: number;
        timestamp: string; // ISO 8601格式，后端生成
      }>;
      activityType: "walking" | "cycling" | "bus" | "subway";
      isFinished: boolean;
      totalDistance: number; // 总距离（公里，后端自动计算）
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误（缺少 userId 或 activityType，或无效的 activityType）
  - `404`: 用户不存在
  - `500`: 服务器错误

### 17. 查询足迹记录

- **接口路径**: `GET /api/footprints`
- **功能**: 查询用户的足迹记录，支持按时间范围和活动类型过滤
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/footprints?userId=ffy6511"
```

- **请求参数**:
  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID（必需）
    startDate?: string; // 开始时间，ISO 8601格式，过滤createdAt
    endDate?: string; // 结束时间，ISO 8601格式，过滤createdAt
    activityType?: "walking" | "cycling" | "bus" | "subway"; // 活动类型过滤
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: Array<{
      id: string;
      userId: string;
      footPrints: Array<{
        latitude: number;
        longitude: number;
        timestamp: string; // ISO 8601格式
      }>;
      activityType: "walking" | "cycling" | "bus" | "subway";
      isFinished: boolean;
      totalDistance: number; // 总距离（公里，后端自动计算）
      createdAt: Date;
      updatedAt: Date;
    }>;
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误（缺少 userId 或无效的 activityType）
  - `404`: 用户不存在
  - `500`: 服务器错误

### 18. 更新足迹记录

- **接口路径**: `PATCH /api/footprints`
- **功能**: 更新足迹记录，可追加新的足迹点或更新活动类型、完成状态
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/footprints" \
  -H "content-type: application/json" \
  -d '{"footprintId":"footprint123","footPrints":[{"latitude":39.9043,"longitude":116.4075}],"isFinished":true}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    footprintId: string; // 足迹记录ID（必需）
    footPrints?: Array<{
      latitude: number; // 纬度（必需）
      longitude: number; // 经度（必需）
      // timestamp 由后端自动生成
    }>; // 新的足迹点，会追加到现有数组
    activityType?: "walking" | "cycling" | "bus" | "subway"; // 更新活动类型
    isFinished?: boolean; // 更新完成状态
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "足迹记录更新成功";
    data: {
      id: string;
      userId: string;
      footPrints: Array<{
        latitude: number;
        longitude: number;
        timestamp: string; // ISO 8601格式
      }>;
      activityType: "walking" | "cycling" | "bus" | "subway";
      isFinished: boolean;
      totalDistance: number; // 总距离（公里，后端自动计算和更新）
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误（缺少足迹记录 ID 或无效的 activityType）
  - `404`: 足迹记录不存在
  - `500`: 服务器错误

### 19. 删除足迹记录

- **接口路径**: `DELETE /api/footprints`
- **功能**: 删除指定的足迹记录
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/footprints" \
  -H "content-type: application/json" \
  -d '{"footprintId":"footprint123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    footprintId: string; // 足迹记录ID（必需）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "足迹记录删除成功";
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少足迹记录 ID
  - `404`: 足迹记录不存在
  - `500`: 服务器错误

### 20. 查询指定足迹记录详情

- **接口路径**: `POST /api/footprints/detail`
- **功能**: 查询指定 ID 的足迹记录详情，包含用户信息
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/footprints/detail" \
  -H "content-type: application/json" \
  -d '{"footprintId":"footprint123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    footprintId: string; // 足迹记录ID
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      id: string;
      userId: string;
      footPrints: Array<{
        latitude: number;
        longitude: number;
        timestamp: string; // ISO 8601格式
      }>;
      activityType: "walking" | "cycling" | "bus" | "subway";
      isFinished: boolean;
      totalDistance: number; // 总距离（公里，后端自动计算）
      createdAt: Date;
      updatedAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 缺少足迹记录 ID
  - `404`: 足迹记录不存在
  - `500`: 服务器错误

### 21. 查询足迹统计

- **接口路径**: `POST /api/footprints/stats`
- **功能**: 查询用户足迹统计，包括总出行次数、距离、时长等，支持按活动类型分组
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/footprints/stats" \
  -H "content-type: application/json" \
  -d '{"userId":"alice","startDate":"2025-09-01T00:00:00Z","endDate":"2025-09-02T23:59:59Z"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 用户ID（必需）
    startDate?: string; // 开始时间，ISO 8601格式，过滤createdAt
    endDate?: string; // 结束时间，ISO 8601格式，过滤createdAt
    activityType?: "walking" | "cycling" | "bus" | "subway"; // 活动类型过滤
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      totalTrips: number; // 总出行次数（所有记录）
      finishedTrips: number; // 已结束次数（isFinished: true）
      ongoingTrips: number; // 进行中次数（isFinished: false）
      totalDistance: number; // 总距离（公里，保留两位小数）
      totalDuration: number; // 总时长（小时，保留两位小数）
      activityStats: {
        walking: {
          trips: number; // 步行出行次数
          distance: number; // 步行总距离（公里）
          duration: number; // 步行总时长（小时）
        }
        cycling: {
          trips: number; // 骑行出行次数
          distance: number; // 骑行总距离（公里）
          duration: number; // 骑行总时长（小时）
        }
        bus: {
          trips: number; // 公交出行次数
          distance: number; // 公交总距离（公里）
          duration: number; // 公交总时长（小时）
        }
        subway: {
          trips: number; // 地铁出行次数
          distance: number; // 地铁总距离（公里）
          duration: number; // 地铁总时长（小时）
        }
      }
    }
  }
  ```
- **状态码**:
  - `200`: 成功
  - `400`: 参数错误（缺少 userId 或无效的 activityType）
  - `404`: 用户不存在
  - `500`: 服务器错误

## 足迹追踪系统使用说明

### 足迹追踪工作流程

1. **开始出行**: 使用 `POST /api/footprints` 创建新的足迹记录，指定活动类型
2. **追踪轨迹**: 在出行过程中，定期使用 `PATCH /api/footprints` 追加新的足迹点
3. **结束出行**: 使用 `PATCH /api/footprints` 将 `isFinished` 设置为 `true`
4. **查看记录**: 使用 `GET /api/footprints` 查询历史足迹记录
5. **查看统计**: 使用 `GET /api/footprints/stats` 查看出行统计数据

### 距离和时长计算说明

- **距离计算**:
  - 使用 Haversine 公式计算相邻足迹点之间的直线距离，累加得到总距离
  - 后端自动计算并存储在 `totalDistance` 字段中（单位：公里）
  - 每次创建或更新足迹点时自动重新计算
  - 精度保留两位小数
- **时长计算**: 使用每条记录中最早和最晚足迹点的时间戳差值计算出行时长
- **精度**: 距离和时长都保留两位小数，单位分别为公里和小时

### 注意事项

- 足迹点的时间戳由后端自动生成，确保时间准确性
- `activityType` 必须是枚举值之一：`walking`、`cycling`、`bus`、`subway`
- 足迹点数组支持追加操作，不会覆盖现有数据
- **距离自动计算**：每次创建或更新足迹点时，后端会自动计算并更新 `totalDistance` 字段
- 统计功能直接使用数据库中存储的距离值，提升查询性能
- 统计功能支持时间范围和活动类型过滤
- 删除足迹记录会永久删除数据，请谨慎操作
- 建议前端在出行过程中定期上传足迹点，避免数据丢失

## 用户日志系统相关 API

### 23. 创建用户日志

- **接口路径**: `POST /api/user-logs`
- **功能**: 创建新的用户日志记录
- **测试性 Curl：**

```bash
curl -i -X POST http://*************:3000/api/user-logs \
  -H "content-type: application/json" \
  -d '{"userId":"alice","recordType":"location","recordId":"checkin123","imageList":["https://example.com/image1.jpg"],"description":"在星巴克打卡","isPublic":true}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    userId: string; // 用户ID
    recordType: "location" | "trip" | "recognition"; // 记录类型
    recordId: string; // 关联记录的ID
    imageList?: string[]; // 图片URL数组，可选
    description?: string; // 用户描述，可选
    isPublic?: boolean; // 是否公开，默认true
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "用户日志创建成功";
    data: {
      id: string;
      userId: string;
      recordType: "location" | "trip" | "recognition";
      recordId: string;
      imageList: string[];
      description: string;
      isPublic: boolean;
      createdAt: Date;
      updatedAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatarURL: string;
      };
      likes: Array<{
        id: string;
        userId: string;
        createdAt: Date;
        user: {
          userId: string;
          nickname: string;
          avatarURL: string;
        };
      }>;
      comments: Array<{
        id: string;
        userId: string;
        content: string;
        replyTo: string | null;
        createdAt: Date;
        user: {
          userId: string;
          nickname: string;
          avatarURL: string;
        };
      }>;
    }
  }
  ```

- **状态码**:

  - `201`: 创建成功
  - `400`: 参数错误
  - `404`: 用户不存在或关联记录不存在
  - `500`: 服务器错误

### 24. 查询用户日志

- **接口路径**: `GET /api/user-logs?userId=alice&recordType=location&isPublic=true&page=1&limit=20`
- **功能**: 查询用户的日志记录，支持分页和筛选
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/user-logs?userId=alice&recordType=location&page=1&limit=10"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 用户ID，必需
    recordType?: "location" | "trip" | "recognition"; // 记录类型筛选，可选
    isPublic?: boolean; // 是否公开筛选，可选
    startDate?: string; // 开始日期，ISO格式，可选
    endDate?: string; // 结束日期，ISO格式，可选
    page?: number; // 页码，默认1
    limit?: number; // 每页数量，默认20，最大100
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    data: {
      logs: Array<{
        id: string;
        userId: string;
        recordType: "location" | "trip" | "recognition";
        recordId: string;
        imageList: string[];
        description: string;
        isPublic: boolean;
        createdAt: Date;
        updatedAt: Date;
        user: {
          userId: string;
          nickname: string;
          avatarURL: string;
        };
        likes: Array<{...}>;
        comments: Array<{...}>;
        LocationCheckIns?: {...}; // 如果recordType是location
        UserFootprints?: {...}; // 如果recordType是trip
        ItemCard?: {...}; // 如果recordType是recognition
      }>;
      pagination: {
        current: number; // 当前页码
        total: number; // 总页数
        count: number; // 总记录数
        limit: number; // 每页数量
        hasNext: boolean; // 是否有下一页
        hasPrev: boolean; // 是否有上一页
      };
    }
  }
  ```

- **状态码**:

  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 25. 更新用户日志

- **接口路径**: `PATCH /api/user-logs`
- **功能**: 更新用户日志的可见性、图片和描述
- **测试性 Curl：**

```bash
curl -i -X PATCH http://*************:3000/api/user-logs \
  -H "content-type: application/json" \
  -d '{"logId":"log123","imageList":["https://example.com/new-image.jpg"],"description":"更新后的描述","isPublic":false}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    logId: string; // 日志ID，必需
    imageList?: string[]; // 新的图片URL数组，可选
    description?: string; // 新的描述，可选
    isPublic?: boolean; // 新的可见性设置，可选
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "日志更新成功";
    data: {
      // 更新后的完整日志对象，格式同创建接口
    }
  }
  ```

- **状态码**:

  - `200`: 更新成功
  - `400`: 参数错误或没有提供要更新的字段
  - `404`: 日志记录不存在
  - `500`: 服务器错误

### 26. 删除用户日志

- **接口路径**: `DELETE /api/user-logs?logId=log123&userId=alice`
- **功能**: 删除用户日志记录（同时删除关联的点赞和评论）
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/user-logs?logId=log123&userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    logId: string; // 日志ID，必需
    userId: string; // 用户ID，必需（用于权限验证）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "日志删除成功";
  }
  ```

- **状态码**:

  - `200`: 删除成功
  - `400`: 参数错误
  - `403`: 无权限删除此日志
  - `404`: 日志记录不存在
  - `500`: 服务器错误

### 27. 创建点赞

- **接口路径**: `POST /api/user-logs/likes`
- **功能**: 为日志记录点赞
- **测试性 Curl：**

```bash
curl -i -X POST http://*************:3000/api/user-logs/likes \
  -H "content-type: application/json" \
  -d '{"logId":"log123","userId":"alice"}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    logId: string; // 日志ID
    userId: string; // 点赞用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "点赞成功";
    data: {
      id: string;
      logId: string;
      userId: string;
      createdAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatarURL: string;
      }
    }
  }
  ```

- **状态码**:

  - `201`: 点赞成功
  - `400`: 参数错误或已经点赞过
  - `404`: 用户不存在或日志记录不存在
  - `500`: 服务器错误

### 28. 取消点赞

- **接口路径**: `DELETE /api/user-logs/likes?logId=log123&userId=alice`
- **功能**: 取消对日志记录的点赞
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/user-logs/likes?logId=log123&userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    logId: string; // 日志ID
    userId: string; // 取消点赞的用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "取消点赞成功";
  }
  ```

- **状态码**:

  - `200`: 取消点赞成功
  - `400`: 参数错误
  - `404`: 点赞记录不存在
  - `500`: 服务器错误

### 29. 创建评论

- **接口路径**: `POST /api/user-logs/comments`
- **功能**: 为日志记录创建评论，支持回复功能
- **测试性 Curl：**

```bash
curl -i -X POST http://*************:3000/api/user-logs/comments \
  -H "content-type: application/json" \
  -d '{"logId":"log123","userId":"alice","content":"很棒的分享！","replyTo":"comment456"}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    logId: string; // 日志ID
    userId: string; // 评论用户ID
    content: string; // 评论内容，不能为空，最大500字符
    replyTo?: string; // 回复的评论ID，可选
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "评论创建成功";
    data: {
      id: string;
      logId: string;
      userId: string;
      content: string;
      replyTo: string | null;
      createdAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatarURL: string;
      }
    }
  }
  ```

- **状态码**:

  - `201`: 评论创建成功
  - `400`: 参数错误（内容为空、超长或回复的评论不属于当前日志）
  - `404`: 用户不存在、日志记录不存在或被回复的评论不存在
  - `500`: 服务器错误

### 30. 删除评论

- **接口路径**: `DELETE /api/user-logs/comments?commentId=comment123&userId=alice`
- **功能**: 删除评论记录（只能删除自己的评论）
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/user-logs/comments?commentId=comment123&userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    commentId: string; // 评论ID
    userId: string; // 用户ID（用于权限验证）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "评论删除成功";
  }
  ```

- **状态码**:

  - `200`: 删除成功
  - `400`: 参数错误
  - `403`: 无权限删除此评论
  - `404`: 评论记录不存在
  - `500`: 服务器错误

**重要变更说明**:

1. **自动日志创建**: 现在创建 ItemCard、LocationCheckIns 和完成 UserFootprints 时会自动创建对应的 UserLogs 记录
2. **日志系统**: 支持三种类型的记录（location、trip、recognition），每种都关联到对应的原始记录
3. **点赞系统**: 防重复点赞机制，自动维护日志的点赞数组
4. **评论系统**: 支持回复功能，评论不可修改只能删除重新创建
5. **权限控制**: 只有日志/评论的创建者才能删除自己的内容
6. **级联删除**: 删除日志时会自动删除关联的点赞和评论（通过数据库外键约束）
7. **分页查询**: 日志查询支持分页、筛选和排序功能

## 地点打卡系统相关 API

### 22. 创建地点打卡记录

- **接口路径**: `POST /api/location-checkins`
- **功能**: 创建新的地点打卡记录
- **测试性 Curl：**

```bash
curl -i -X POST http://*************:3000/api/location-checkins \
  -H "content-type: application/json" \
  -d '{"userId":"alice","position":"星巴克","latitude":39.9042,"longitude":116.4074}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 用户ID（必需）
    position?: string; // 地点名称，如"星巴克"（可选，最大100字符）
    latitude: number; // 纬度（必需，-90到90之间）
    longitude: number; // 经度（必需，-180到180之间）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "地点打卡记录创建成功";
    data: {
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `201`: 创建成功
  - `400`: 参数错误或验证失败
  - `404`: 用户不存在
  - `500`: 服务器错误

### 23. 查询地点打卡记录

- **接口路径**: `GET /api/location-checkins`
- **功能**: 查询指定用户的所有地点打卡记录，支持按时间范围过滤
- **测试性 Curl：**

```bash
curl -i -X GET http://*************:3000/api/location-checkins？userId=ffy6511
```

- **请求参数**:
  ```typescript
  // Qurey 参数
  {
    userId: string; // 用户ID（必需）
    startDate?: string; // 开始时间（可选，ISO 8601格式）
    endDate?: string; // 结束时间（可选，ISO 8601格式）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "查询成功";
    data: Array<{
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      createdAt: Date;
      updatedAt: Date;
    }>;
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 24. 更新地点打卡记录

- **接口路径**: `PATCH /api/location-checkins`
- **功能**: 更新指定地点打卡记录的信息
- **测试性 Curl：**

```bash
curl -i -X PATCH http://*************:3000/api/location-checkins \
  -H "content-type: application/json" \
  -d '{"id":"clxxx123","position":"麦当劳","latitude":39.9043,"longitude":116.4075}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    id: string; // 打卡记录ID（必需）
    position?: string; // 地点名称（可选，最大100字符）
    latitude?: number; // 纬度（可选，-90到90之间）
    longitude?: number; // 经度（可选，-180到180之间）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "地点打卡记录更新成功";
    data: {
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      createdAt: Date;
      updatedAt: Date;
    }
  }
  ```
- **状态码**:
  - `200`: 更新成功
  - `400`: 参数错误或验证失败
  - `404`: 记录不存在
  - `500`: 服务器错误

### 25. 删除地点打卡记录

- **接口路径**: `DELETE /api/location-checkins`
- **功能**: 删除指定的地点打卡记录
- **测试性 Curl：**

```bash
curl -i -X DELETE http://*************:3000/api/location-checkins \
  -H "content-type: application/json" \
  -d '{"id":"clxxx123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    id: string; // 打卡记录ID（必需）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "地点打卡记录删除成功";
  }
  ```
- **状态码**:
  - `200`: 删除成功
  - `400`: 参数错误
  - `404`: 记录不存在
  - `500`: 服务器错误

### 26. 查询指定地点打卡记录详情

- **接口路径**: `POST /api/location-checkins/detail`
- **功能**: 查询指定 ID 的地点打卡记录详情，包含用户信息
- **测试性 Curl：**

```bash
curl -i -X POST http://*************:3000/api/location-checkins/detail \
  -H "content-type: application/json" \
  -d '{"id":"clxxx123"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    id: string; // 打卡记录ID（必需）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "查询成功";
    data: {
      id: string;
      userId: string;
      position: string | null;
      latitude: number;
      longitude: number;
      createdAt: Date;
      updatedAt: Date;
      user: {
        userId: string;
        nickname: string;
        avatar: string | null;
        avatarURL: string | null;
      }
    }
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 记录不存在
  - `500`: 服务器错误

### 27. 查询地点打卡统计

- **接口路径**: `POST /api/location-checkins/stats`
- **功能**: 查询用户地点打卡统计，包括总打卡次数、按地点分组的打卡次数
- **测试性 Curl：**

```bash
curl -i -X POST http://*************:3000/api/location-checkins/stats \
  -H "content-type: application/json" \
  -d '{"userId":"alice","startDate":"2024-01-01T00:00:00.000Z","endDate":"2024-12-31T23:59:59.999Z"}'
```

- **请求参数**:
  ```typescript
  // Body 参数
  {
    userId: string; // 用户ID（必需）
    startDate?: string; // 开始时间（可选，ISO 8601格式）
    endDate?: string; // 结束时间（可选，ISO 8601格式）
  }
  ```
- **响应数据**:
  ```typescript
  // 成功响应
  {
    success: true;
    message: "统计查询成功";
    data: {
      totalCheckIns: number; // 总打卡次数
      uniquePositionsCount: number; // 不同地点的个数
      positionStats: Array<{
        position: string; // 地点名称
        count: number; // 打卡次数
      }>; // 按打卡次数从多到少排序
      timeRange: {
        startDate: string | null;
        endDate: string | null;
      }
    }
  }
  ```
- **状态码**:
  - `200`: 查询成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

### 地点打卡工作流程

```
创建打卡 → 查看记录 → 更新信息 → 统计分析
    ↓         ↓         ↓         ↓
  POST      GET      PATCH     POST
/location- /location- /location- /location-
checkins   checkins   checkins   checkins/stats
```

### 地点打卡注意事项

- 经纬度参数必须在有效范围内（纬度：-90 到 90，经度：-180 到 180）
- position 字段可选，最大长度 100 字符，用于存储地点名称
- 创建时间 createdAt 即为打卡时间，由系统自动生成
- 支持按时间范围过滤查询和统计
- 统计功能按地点分组，返回热门地点排行
- 删除操作会永久删除数据，请谨慎操作
- 建议结合地图组件使用，提供更好的用户体验
